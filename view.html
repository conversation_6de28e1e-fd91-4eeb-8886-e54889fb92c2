<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Multi-Language Grammar Checker</title>
  <style>
    :root {
      --primary: #2563eb;
      --primary-dark: #1e40af;
      --background: #f3f4f6;
      --card-bg: #fff;
      --border: #e5e7eb;
      --text: #1f2937;
      --muted: #6b7280;
      --success: #22c55e;
      --error: #ef4444;
    }

    html,
    body {
      height: 100%;
      margin: 0;
      padding: 0;
      font-family: 'Segoe UI', 'Roboto', Arial, sans-serif;
      background: var(--background);
      color: var(--text);
    }

    body {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .container {
      width: 100%;
      max-width: 480px;
      background: var(--card-bg);
      border-radius: 18px;
      box-shadow: 0 4px 32px rgba(30, 64, 175, 0.08), 0 1.5px 6px rgba(0, 0, 0, 0.04);
      padding: 32px 24px 28px 24px;
      margin: 24px;
      display: flex;
      flex-direction: column;
      gap: 18px;
    }

    h1 {
      text-align: center;
      font-size: 2rem;
      font-weight: 700;
      color: var(--primary-dark);
      margin-bottom: 0.5em;
      letter-spacing: -1px;
    }

    .label {
      font-weight: 600;
      margin-bottom: 6px;
      color: var(--muted);
      font-size: 1rem;
      display: block;
    }

    textarea {
      width: 100%;
      min-height: 120px;
      padding: 14px 14px;
      font-size: 1.08rem;
      border-radius: 8px;
      border: 1.5px solid var(--border);
      margin-bottom: 0;
      resize: vertical;
      background: #f9fafb;
      color: var(--text);
      transition: border 0.2s;
    }

    textarea:focus {
      outline: none;
      border-color: var(--primary);
      background: #fff;
    }

    .row {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      align-items: center;
      margin-bottom: 0;
    }

    select {
      padding: 10px 12px;
      font-size: 1rem;
      border-radius: 7px;
      border: 1.5px solid var(--border);
      background: #f9fafb;
      color: var(--text);
      transition: border 0.2s;
      min-width: 120px;
      max-width: 100%;
    }

    select:focus {
      outline: none;
      border-color: var(--primary);
      background: #fff;
    }

    button {
      padding: 11px 22px;
      font-size: 1.08rem;
      border-radius: 7px;
      border: none;
      background: var(--primary);
      color: #fff;
      font-weight: 600;
      cursor: pointer;
      transition: background 0.18s, box-shadow 0.18s;
      box-shadow: 0 1.5px 6px rgba(37, 99, 235, 0.08);
    }

    button:hover:not(:disabled),
    button:focus:not(:disabled) {
      background: var(--primary-dark);
    }

    button:disabled {
      background: #cbd5e1;
      color: #fff;
      cursor: not-allowed;
    }

    .result {
      margin-top: 10px;
      background: #f8fafc;
      padding: 16px 14px;
      border-radius: 8px;
      border: 1.5px solid var(--border);
      font-size: 1.05rem;
      color: var(--text);
      word-break: break-word;
      box-shadow: 0 1.5px 6px rgba(30, 64, 175, 0.04);
    }

    .result b {
      color: var(--primary-dark);
      font-weight: 600;
    }
    .model-selection {
      margin-bottom: 18px;
      padding: 16px;
      background: #f8fafc;
      border-radius: 8px;
      border: 1.5px solid var(--border);
    }
    .checkbox-group {
      display: flex;
      gap: 20px;
      margin-bottom: 12px;
      flex-wrap: wrap;
    }
    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .checkbox-item input[type="checkbox"] {
      width: 18px;
      height: 18px;
      accent-color: var(--primary);
    }
    .checkbox-item label {
      font-weight: 500;
      color: var(--text);
      cursor: pointer;
      margin: 0;
    }
    .api-key-section {
      margin-top: 12px;
      display: none;
    }
    .api-key-section input {
      width: 100%;
      padding: 10px 12px;
      border: 1.5px solid var(--border);
      border-radius: 7px;
      font-size: 0.95rem;
      background: #fff;
      color: var(--text);
      transition: border 0.2s;
    }
    .api-key-section input:focus {
      outline: none;
      border-color: var(--primary);
    }
    .api-key-section label {
      display: block;
      margin-bottom: 6px;
      font-weight: 600;
      color: var(--muted);
      font-size: 0.9rem;
    }

    @media (max-width: 600px) {
      .container {
        padding: 18px 4vw 16px 4vw;
        max-width: 98vw;
      }

      h1 {
        font-size: 1.3rem;
      }

      textarea,
      select,
      button {
        font-size: 1rem;
      }

      .row {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
      }
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>Multi-Language Grammar Checker</h1>

    <div class="model-selection">
      <div class="checkbox-group">
        <div class="checkbox-item">
          <input type="checkbox" id="useLocal" checked>
          <label for="useLocal">Use Local Language Model</label>
        </div>
        <div class="checkbox-item">
          <input type="checkbox" id="useOpenAI">
          <label for="useOpenAI">OpenAI</label>
        </div>
      </div>
      <div class="api-key-section" id="apiKeySection">
        <label for="apiKeyInput">OpenAI API Key:</label>
        <input type="password" id="apiKeyInput" placeholder="Enter your OpenAI API key">
      </div>
    </div>

    <label class="label" for="inputText">Enter text:</label>
    <textarea id="inputText" placeholder="Type or paste your text here..."></textarea>
    <div class="row">
      <div style="flex:1; min-width:120px;">
        <label for="language" class="label" style="margin-bottom:2px;">Language:</label>
        <select id="language">
          <option value="auto">Auto Detect</option>
          <option value="af">Afrikaans</option>
          <option value="sq">Albanian</option>
          <option value="am">Amharic</option>
          <option value="ar">Arabic</option>
          <option value="hy">Armenian</option>
          <option value="az">Azerbaijani</option>
          <option value="eu">Basque</option>
          <option value="be">Belarusian</option>
          <option value="bn">Bengali</option>
          <option value="bs">Bosnian</option>
          <option value="bg">Bulgarian</option>
          <option value="ca">Catalan</option>
          <option value="ceb">Cebuano</option>
          <option value="ny">Chichewa</option>
          <option value="zh-CN">Chinese (Simplified)</option>
          <option value="zh-TW">Chinese (Traditional)</option>
          <option value="co">Corsican</option>
          <option value="hr">Croatian</option>
          <option value="cs">Czech</option>
          <option value="da">Danish</option>
          <option value="nl">Dutch</option>
          <option value="en">English</option>
          <option value="eo">Esperanto</option>
          <option value="et">Estonian</option>
          <option value="tl">Filipino</option>
          <option value="fi">Finnish</option>
          <option value="fr">French</option>
          <option value="fy">Frisian</option>
          <option value="gl">Galician</option>
          <option value="ka">Georgian</option>
          <option value="de">German</option>
          <option value="el">Greek</option>
          <option value="gu">Gujarati</option>
          <option value="ht">Haitian Creole</option>
          <option value="ha">Hausa</option>
          <option value="haw">Hawaiian</option>
          <option value="iw">Hebrew</option>
          <option value="hi">Hindi</option>
          <option value="hmn">Hmong</option>
          <option value="hu">Hungarian</option>
          <option value="is">Icelandic</option>
          <option value="ig">Igbo</option>
          <option value="id">Indonesian</option>
          <option value="ga">Irish</option>
          <option value="it">Italian</option>
          <option value="ja">Japanese</option>
          <option value="jw">Javanese</option>
          <option value="kn">Kannada</option>
          <option value="kk">Kazakh</option>
          <option value="km">Khmer</option>
          <option value="ko">Korean</option>
          <option value="ku">Kurdish (Kurmanji)</option>
          <option value="ky">Kyrgyz</option>
          <option value="lo">Lao</option>
          <option value="la">Latin</option>
          <option value="lv">Latvian</option>
          <option value="lt">Lithuanian</option>
          <option value="lb">Luxembourgish</option>
          <option value="mk">Macedonian</option>
          <option value="mg">Malagasy</option>
          <option value="ms">Malay</option>
          <option value="ml">Malayalam</option>
          <option value="mt">Maltese</option>
          <option value="mi">Maori</option>
          <option value="mr">Marathi</option>
          <option value="mn">Mongolian</option>
          <option value="my">Myanmar (Burmese)</option>
          <option value="ne">Nepali</option>
          <option value="no">Norwegian</option>
          <option value="ps">Pashto</option>
          <option value="fa">Persian</option>
          <option value="pl">Polish</option>
          <option value="pt">Portuguese</option>
          <option value="pa">Punjabi</option>
          <option value="ro">Romanian</option>
          <option value="ru">Russian</option>
          <option value="sm">Samoan</option>
          <option value="gd">Scots Gaelic</option>
          <option value="sr">Serbian</option>
          <option value="st">Sesotho</option>
          <option value="sn">Shona</option>
          <option value="sd">Sindhi</option>
          <option value="si">Sinhala</option>
          <option value="sk">Slovak</option>
          <option value="sl">Slovenian</option>
          <option value="so">Somali</option>
          <option value="es">Spanish</option>
          <option value="su">Sundanese</option>
          <option value="sw">Swahili</option>
          <option value="sv">Swedish</option>
          <option value="tg">Tajik</option>
          <option value="ta">Tamil</option>
          <option value="te">Telugu</option>
          <option value="th">Thai</option>
          <option value="tr">Turkish</option>
          <option value="uk">Ukrainian</option>
          <option value="ur">Urdu</option>
          <option value="uz">Uzbek</option>
          <option value="vi">Vietnamese</option>
          <option value="cy">Welsh</option>
          <option value="xh">Xhosa</option>
          <option value="yi">Yiddish</option>
          <option value="yo">Yoruba</option>
          <option value="zu">Zulu</option>
        </select>
      </div>
      <div style="flex:1; min-width:120px;">
        <label for="translateTo" class="label" style="margin-bottom:2px;">Translate to:</label>
        <select id="translateTo">
          <option value="none">None</option>
          <option value="af">Afrikaans</option>
          <option value="sq">Albanian</option>
          <option value="am">Amharic</option>
          <option value="ar">Arabic</option>
          <option value="hy">Armenian</option>
          <option value="az">Azerbaijani</option>
          <option value="eu">Basque</option>
          <option value="be">Belarusian</option>
          <option value="bn">Bengali</option>
          <option value="bs">Bosnian</option>
          <option value="bg">Bulgarian</option>
          <option value="ca">Catalan</option>
          <option value="ceb">Cebuano</option>
          <option value="ny">Chichewa</option>
          <option value="zh-CN">Chinese (Simplified)</option>
          <option value="zh-TW">Chinese (Traditional)</option>
          <option value="co">Corsican</option>
          <option value="hr">Croatian</option>
          <option value="cs">Czech</option>
          <option value="da">Danish</option>
          <option value="nl">Dutch</option>
          <option value="en">English</option>
          <option value="eo">Esperanto</option>
          <option value="et">Estonian</option>
          <option value="tl">Filipino</option>
          <option value="fi">Finnish</option>
          <option value="fr">French</option>
          <option value="fy">Frisian</option>
          <option value="gl">Galician</option>
          <option value="ka">Georgian</option>
          <option value="de">German</option>
          <option value="el">Greek</option>
          <option value="gu">Gujarati</option>
          <option value="ht">Haitian Creole</option>
          <option value="ha">Hausa</option>
          <option value="haw">Hawaiian</option>
          <option value="iw">Hebrew</option>
          <option value="hi">Hindi</option>
          <option value="hmn">Hmong</option>
          <option value="hu">Hungarian</option>
          <option value="is">Icelandic</option>
          <option value="ig">Igbo</option>
          <option value="id">Indonesian</option>
          <option value="ga">Irish</option>
          <option value="it">Italian</option>
          <option value="ja">Japanese</option>
          <option value="jw">Javanese</option>
          <option value="kn">Kannada</option>
          <option value="kk">Kazakh</option>
          <option value="km">Khmer</option>
          <option value="ko">Korean</option>
          <option value="ku">Kurdish (Kurmanji)</option>
          <option value="ky">Kyrgyz</option>
          <option value="lo">Lao</option>
          <option value="la">Latin</option>
          <option value="lv">Latvian</option>
          <option value="lt">Lithuanian</option>
          <option value="lb">Luxembourgish</option>
          <option value="mk">Macedonian</option>
          <option value="mg">Malagasy</option>
          <option value="ms">Malay</option>
          <option value="ml">Malayalam</option>
          <option value="mt">Maltese</option>
          <option value="mi">Maori</option>
          <option value="mr">Marathi</option>
          <option value="mn">Mongolian</option>
          <option value="my">Myanmar (Burmese)</option>
          <option value="ne">Nepali</option>
          <option value="no">Norwegian</option>
          <option value="ps">Pashto</option>
          <option value="fa">Persian</option>
          <option value="pl">Polish</option>
          <option value="pt">Portuguese</option>
          <option value="pa">Punjabi</option>
          <option value="ro">Romanian</option>
          <option value="ru">Russian</option>
          <option value="sm">Samoan</option>
          <option value="gd">Scots Gaelic</option>
          <option value="sr">Serbian</option>
          <option value="st">Sesotho</option>
          <option value="sn">Shona</option>
          <option value="sd">Sindhi</option>
          <option value="si">Sinhala</option>
          <option value="sk">Slovak</option>
          <option value="sl">Slovenian</option>
          <option value="so">Somali</option>
          <option value="es">Spanish</option>
          <option value="su">Sundanese</option>
          <option value="sw">Swahili</option>
          <option value="sv">Swedish</option>
          <option value="tg">Tajik</option>
          <option value="ta">Tamil</option>
          <option value="te">Telugu</option>
          <option value="th">Thai</option>
          <option value="tr">Turkish</option>
          <option value="uk">Ukrainian</option>
          <option value="ur">Urdu</option>
          <option value="uz">Uzbek</option>
          <option value="vi">Vietnamese</option>
          <option value="cy">Welsh</option>
          <option value="xh">Xhosa</option>
          <option value="yi">Yiddish</option>
          <option value="yo">Yoruba</option>
          <option value="zu">Zulu</option>
        </select>
      </div>
      <div style="display:flex; align-items:end;">
        <button id="checkBtn">Check Grammar</button>
      </div>
    </div>
    <div id="result" class="result" style="display:none;"></div>
    <div id="translation" class="result" style="display:none;"></div>
  </div>
  <script>
    const openaiEndpoint = "https://api.openai.com/v1/chat/completions";
    const localEndpoint = "http://localhost:1234/v1/chat/completions"; // Default Ollama endpoint

    // Handle checkbox logic
    document.getElementById('useLocal').onchange = function() {
      if (this.checked) {
        document.getElementById('useOpenAI').checked = false;
        document.getElementById('apiKeySection').style.display = 'none';
      }
    };

    document.getElementById('useOpenAI').onchange = function() {
      if (this.checked) {
        document.getElementById('useLocal').checked = false;
        document.getElementById('apiKeySection').style.display = 'block';
      } else {
        document.getElementById('apiKeySection').style.display = 'none';
      }
    };

    document.getElementById('checkBtn').onclick = async function () {
      const inputText = document.getElementById('inputText').value.trim();
      const language = document.getElementById('language').value;
      const translateTo = document.getElementById('translateTo').value;
      const useLocal = document.getElementById('useLocal').checked;
      const useOpenAI = document.getElementById('useOpenAI').checked;
      const apiKey = document.getElementById('apiKeyInput').value.trim();
      const resultDiv = document.getElementById('result');
      const translationDiv = document.getElementById('translation');

      resultDiv.style.display = 'none';
      translationDiv.style.display = 'none';
      resultDiv.textContent = '';
      translationDiv.textContent = '';

      if (!inputText) {
        alert('Please enter some text.');
        return;
      }

      if (!useLocal && !useOpenAI) {
        alert('Please select at least one model type (Local or OpenAI).');
        return;
      }

      if (useOpenAI && !apiKey) {
        alert('Please enter your OpenAI API key.');
        return;
      }

      this.disabled = true;
      this.textContent = 'Checking...';
      let systemPrompt = '';
      let expectJson = false;
      if (language === 'auto') {
        systemPrompt = `You are a grammar expert. Detect the language of the following text, then correct any grammar, spelling, or style mistakes. Reply ONLY in this strict JSON format: { "language": "Detected Language", "corrected": "Corrected text" }`;
        expectJson = true;
      } else {
        systemPrompt = `You are a grammar expert for the ${getLanguageName(language)} language. Correct any grammar, spelling, or style mistakes in the following text. Reply ONLY in this strict JSON format: { "corrected": "Corrected text" }`;
        expectJson = true;
      }
      try {
        let data;
        let modelUsed = '';

        if (useOpenAI) {
          modelUsed = 'OpenAI GPT-4';
          const response = await fetch(openaiEndpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${apiKey}`
            },
            body: JSON.stringify({
              model: 'gpt-4',
              messages: [
                { role: 'system', content: systemPrompt },
                { role: 'user', content: inputText }
              ],
              temperature: 0.2
            })
          });
          data = await response.json();
        } else if (useLocal) {
          modelUsed = 'google/gemma-3-12b';
          data = await makeLocalRequest(systemPrompt, inputText);
        }
        if ((data.choices && data.choices.length > 0) || data.response) {
          let output = data.choices ? data.choices[0].message.content : data.response;
          let detectedLanguage = '';
          let correctedText = '';
          if (expectJson) {
            try {
              // Extract JSON from the response
              const jsonMatch = output.match(/\{[\s\S]*\}/);
              if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                if (language === 'auto') {
                  detectedLanguage = parsed.language || '';
                }
                correctedText = parsed.corrected || '';
              } else {
                correctedText = output;
              }
            } catch (e) {
              correctedText = output;
            }
          } else {
            correctedText = output;
          }
          let html = '';
          html += `<b>Model Used:</b> ${modelUsed}<br>`;
          if (detectedLanguage) {
            html += `<b>Detected Language:</b> ${detectedLanguage}<br>`;
          }
          html += `<b>Corrected Text:</b><br>${correctedText.replace(/\n/g, '<br>')}`;
          resultDiv.innerHTML = html;
          resultDiv.style.display = 'block';
          // If translation is requested
          if (translateTo !== 'none') {
            translationDiv.style.display = 'block';
            translationDiv.innerHTML = '<i>Translating...</i>';
            const translationPrompt = `Translate the following text to ${getLanguageName(translateTo)}. Reply ONLY in this strict JSON format: { "translation": "Translated text" }\n\nText:\n${correctedText}\n\nReturn the translation using the English (Latin) alphabet only, even if the target language uses a different script. If the language is already in the Latin alphabet, return as usual.`;

            let translationData;
            if (useOpenAI) {
              const translationResponse = await fetch(openaiEndpoint, {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${apiKey}`
                },
                body: JSON.stringify({
                  model: 'gpt-4',
                  messages: [
                    { role: 'system', content: 'You are a helpful translator.' },
                    { role: 'user', content: translationPrompt }
                  ],
                  temperature: 0.2
                })
              });
              translationData = await translationResponse.json();
            } else if (useLocal) {
              translationData = await makeLocalRequest('You are a helpful translator.', translationPrompt);
            }

            if ((translationData.choices && translationData.choices.length > 0) || translationData.response) {
              let translationOutput = translationData.choices ? translationData.choices[0].message.content : translationData.response;
              let translationText = '';
              try {
                const jsonMatch = translationOutput.match(/\{[\s\S]*\}/);
                if (jsonMatch) {
                  const parsed = JSON.parse(jsonMatch[0]);
                  translationText = parsed.translation || '';
                } else {
                  translationText = translationOutput;
                }
              } catch (e) {
                translationText = translationOutput;
              }
              translationDiv.innerHTML = `<b>Translation (${getLanguageName(translateTo)}):</b><br>${translationText.replace(/\n/g, '<br>')}`;
            } else {
              translationDiv.innerHTML = `No translation response from ${modelUsed}.`;
            }
          }
        } else {
          resultDiv.textContent = `No response from ${modelUsed}.`;
          resultDiv.style.display = 'block';
        }
      } catch (err) {
        resultDiv.textContent = 'Error: ' + err.message;
        resultDiv.style.display = 'block';
        console.error('Request error:', err);
      } finally {
        this.disabled = false;
        this.textContent = 'Check Grammar';
      }
    };

    // Helper function for local model requests
    async function makeLocalRequest(systemPrompt, userPrompt) {
      try {
        const response = await fetch(localEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            model: 'google/gemma-3-12b', // You can make this configurable
            prompt: `${systemPrompt}\n\nUser: ${userPrompt}\n\nAssistant:`,
            stream: false,
            options: {
              temperature: 0.2
            }
          })
        });

        if (!response.ok) {
          throw new Error(`Local model request failed: ${response.status}`);
        }

        const data = await response.json();
        return { response: data.response || data.text || 'No response from local model' };
      } catch (error) {
        console.error('Local model error:', error);
        throw new Error(`Local model connection failed: ${error.message}. Make sure your local model server is running.`);
      }
    }

    function getLanguageName(code) {
      const map = {
        en: 'English', es: 'Spanish', fr: 'French', de: 'German', it: 'Italian', pt: 'Portuguese', ru: 'Russian', zh: 'Chinese', ja: 'Japanese', ko: 'Korean'
      };
      return map[code] || code;
    }
  </script>
</body>

</html>